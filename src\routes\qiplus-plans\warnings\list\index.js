/**
 * Warnings List Component
 */
import moment from 'moment'
import React, { useCallback, useEffect, useState } from 'react'
import { connect } from 'react-redux'
import { Link } from 'react-router-dom'

// Components
import LinearProgress from '@material-ui/core/LinearProgress'
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'
import { FirebaseRepository } from 'FirebaseRef/repository'
import { Badge } from 'reactstrap'

// Helpers
import { mockWarnings } from '../model'

const WarningsList = (props) => {
  const [warnings, setWarnings] = useState([])
  const [loading, setLoading] = useState(false)
  const [layout, setLayout] = useState('grid')
  const [authorsData, setAuthorsData] = useState({}) // Cache dos dados dos autores

  // Firebase repository instance
  const repository = new FirebaseRepository()
  const collectionName = 'warnings'
  const qiusersCollectionName = 'qiusers'

  // Função para buscar dados dos autores usando where uid = authorId
  const loadAuthorsData = useCallback(async (warningsData) => {
    const authorIds = [...new Set(
      warningsData
        .map(warning => warning.created_by)
        .filter(id => id && id !== 'system' && !authorsData[id])
    )]

    if (authorIds.length === 0) return

    console.log('🔍 Carregando dados dos autores:', authorIds)

    // Buscar cada autor usando where uid = authorId
    const authorsPromises = authorIds.map(async (authorId) => {
      return new Promise((resolve) => {
        repository.getCollection(
          qiusersCollectionName,
          (users) => {
            if (users && users.length > 0) {
              const userData = users[0] // Pegar o primeiro (e único) resultado
              console.log(`✅ Autor ${authorId} carregado:`, userData.displayName || userData.firstName)
              resolve({ id: authorId, data: userData })
            } else {
              console.warn(`⚠️ Autor ${authorId} não encontrado`)
              resolve({ id: authorId, data: null })
            }
          },
          (error) => {
            console.error(`❌ Erro ao carregar autor ${authorId}:`, error)
            resolve({ id: authorId, data: null })
          },
          null, // orderBy
          null, // order
          [['uid', '==', authorId]], // where clause
          1 // limit
        )
      })
    })

    const authorsResults = await Promise.all(authorsPromises)

    const newAuthorsData = { ...authorsData }
    authorsResults.forEach(({ id, data }) => {
      newAuthorsData[id] = data || { displayName: 'Usuário não encontrado', firstName: 'Usuário não encontrado' }
    })

    setAuthorsData(newAuthorsData)
  }, [repository, qiusersCollectionName, authorsData])

  // Função para obter nome do autor
  const getAuthorName = useCallback((warning) => {
    if (!warning.created_by || warning.created_by === 'system') {
      return 'Sistema'
    }

    const authorData = authorsData[warning.created_by]
    if (authorData) {
      return authorData.displayName || `${authorData.firstName} ${authorData.lastName || ''}`.trim()
    }

    return warning.author || 'Carregando...'
  }, [authorsData])

  useEffect(() => {
    loadWarnings()
  }, [])

  const loadWarnings = () => {
    setLoading(true)

    // Carregar warnings do Firebase
    repository.getCollection(
      collectionName,
      (data) => {
        console.log('✅ Warnings loaded from Firebase:', data)
        if (data && data.length > 0) {
          // Normalizar timestamps em todos os warnings (converter milissegundos para segundos se necessário)
          const normalizedWarnings = data.map(warning => ({
            ...warning,
            createdAt: (() => {
              if (typeof warning.createdAt === 'number') {
                // Se está em milissegundos, converter para segundos
                return warning.createdAt > 9999999999 ? Math.floor(warning.createdAt / 1000) : warning.createdAt
              }
              if (typeof warning.createdAt === 'string') {
                const parsed = parseInt(warning.createdAt, 10)
                if (!isNaN(parsed)) {
                  // Se está em milissegundos, converter para segundos
                  return parsed > 9999999999 ? Math.floor(parsed / 1000) : parsed
                }
              }
              return null
            })(),
            updatedAt: (() => {
              if (typeof warning.updatedAt === 'number') {
                // Se está em milissegundos, converter para segundos
                return warning.updatedAt > 9999999999 ? Math.floor(warning.updatedAt / 1000) : warning.updatedAt
              }
              if (typeof warning.updatedAt === 'string') {
                const parsed = parseInt(warning.updatedAt, 10)
                if (!isNaN(parsed)) {
                  // Se está em milissegundos, converter para segundos
                  return parsed > 9999999999 ? Math.floor(parsed / 1000) : parsed
                }
              }
              return null
            })()
          }))

          console.log('🔍 Timestamps normalizados:', normalizedWarnings.map(w => ({
            id: w.id,
            createdAt: { value: w.createdAt, type: typeof w.createdAt },
            updatedAt: { value: w.updatedAt, type: typeof w.updatedAt }
          })))
          // Ordenar por data de criação (mais recentes primeiro)
          const sortedWarnings = normalizedWarnings.sort((a, b) => {
            // Função para obter timestamp válido (já normalizado)
            const getValidTimestamp = (item) => {
              // Usar createdAt já normalizado (em segundos)
              if (typeof item.createdAt === 'number' && item.createdAt > 0) {
                return item.createdAt
              }

              // Fallback para campo 'date' (formato ISO)
              if (item.date) {
                const parsed = moment(item.date)
                if (parsed.isValid()) {
                  return parsed.unix()
                }
              }

              // Último fallback: timestamp atual
              return moment().unix()
            }

            const timestampA = getValidTimestamp(a)
            const timestampB = getValidTimestamp(b)
            return timestampB - timestampA
          })
          setWarnings(sortedWarnings)

          // Carregar dados dos autores
          loadAuthorsData(sortedWarnings)
        } else {
          console.log('📝 No warnings found in Firebase, using mock data')
          setWarnings(mockWarnings)
        }
        setLoading(false)
      },
      (error) => {
        console.error('❌ Error loading warnings from Firebase:', error)
        console.log('📝 Using mock data as fallback')
        setWarnings(mockWarnings)
        setLoading(false)
      },
      null,        // orderBy - removido para evitar erros com campos inexistentes
      null,        // order
      null,        // where
      50           // limit
    )
  }

  return (
    <div className="data-table-wrapper">
      <div className="page-title d-flex justify-content-between align-items-center">
        <div className="page-title-wrap">
          <i className="ti-announcement"></i>
          <h2>
            <span>Avisos</span>
          </h2>
        </div>
        <div className="page-title-right">
          <Link to="/qiplus-plans/warnings/add" className="btn btn-primary btn-sm">
            <i className="ti-plus"></i> Novo Aviso
          </Link>
        </div>
      </div>

      {loading && <LinearProgress />}

      <div className="main-content">
        {warnings.length > 0 ? (
          <div className="row">
            {warnings.map((warning, index) => {
              const detailLink = `/qiplus-plans/warnings/${warning.id}`

              // Função para obter data formatada de forma segura
              const getFormattedDate = (item, createdField = true) => {
                const primaryField = createdField ? 'createdAt' : 'updatedAt'
                const fallbackField = createdField ? 'date' : 'modified'

                // Priorizar campo principal (Unix timestamp)
                if (typeof item[primaryField] === 'number' && item[primaryField] > 0) {
                  return moment.unix(item[primaryField]).format('DD/MM/YYYY HH:mm')
                }

                // Tentar converter campo principal se for string
                if (item[primaryField] && typeof item[primaryField] === 'string') {
                  const parsed = moment(item[primaryField])
                  if (parsed.isValid()) {
                    return parsed.format('DD/MM/YYYY HH:mm')
                  }
                }

                // Fallback para campo alternativo (formato ISO)
                if (item[fallbackField]) {
                  const parsed = moment(item[fallbackField])
                  if (parsed.isValid()) {
                    return parsed.format('DD/MM/YYYY HH:mm')
                  }
                }

                // Último fallback: data atual
                return moment().format('DD/MM/YYYY HH:mm')
              }

              const createdDate = getFormattedDate(warning, true)
              const modifiedDate = getFormattedDate(warning, false)

              return (
                <RctCollapsibleCard
                  key={index}
                  customClasses=""
                  colClasses="col-sm-12 col-md-6 col-lg-4 w-xs-full"
                  heading={
                    <div className="d-flex justify-content-between align-items-center">
                      <Link to={detailLink}>
                        <span className={!warning.active ? 'text-muted' : ''}>{warning.title}</span>
                      </Link>
                      <Badge color={warning.active ? 'success' : 'secondary'} className="ml-2">
                        {warning.active ? 'Ativo' : 'Inativo'}
                      </Badge>
                    </div>
                  }
                >
                  <div className="warning-card-content">
                    <div className="mb-15">
                      <h6 className="mb-5">Conteúdo do App:</h6>
                      <p className="text-muted mb-0" style={{ fontSize: '0.875rem', lineHeight: '1.4' }}>
                        {warning.content_app.length > 120
                          ? `${warning.content_app.substring(0, 120)}...`
                          : warning.content_app
                        }
                      </p>
                    </div>

                    <div className="warning-meta">
                      <div className="d-flex justify-content-between align-items-center mb-10">
                        <small className="text-muted">
                          <i className="ti-calendar mr-5"></i>
                          Criado: {createdDate}
                        </small>
                      </div>

                      <div className="d-flex justify-content-between align-items-center mb-10">
                        <small className="text-muted">
                          <i className="ti-pencil mr-5"></i>
                          Modificado: {modifiedDate}
                        </small>
                      </div>

                      <div className="d-flex justify-content-between align-items-center">
                        <small className="text-muted">
                          <i className="ti-user mr-5"></i>
                          Autor: {getAuthorName(warning)}
                        </small>
                      </div>
                    </div>

                    <div className="warning-actions mt-15 pt-15" style={{ borderTop: '1px solid #eee' }}>
                      <div className="d-flex justify-content-between">
                        <Link to={detailLink} className="btn btn-outline-primary btn-sm">
                          <i className="ti-eye mr-5"></i>
                          Visualizar
                        </Link>
                        <Link to={`${detailLink}/edit`} className="btn btn-outline-secondary btn-sm">
                          <i className="ti-pencil mr-5"></i>
                          Editar
                        </Link>
                      </div>
                    </div>
                  </div>
                </RctCollapsibleCard>
              )
            })}
          </div>
        ) : (
          <RctCollapsibleCard>
            <div className="text-center py-5">
              <i className="ti-announcement" style={{ fontSize: '3rem', color: '#ccc' }}></i>
              <h4 className="mt-3 mb-2">Nenhum aviso encontrado</h4>
              <p className="text-muted mb-4">Ainda não há avisos cadastrados no sistema.</p>
              <Link to="/qiplus-plans/warnings/add" className="btn btn-primary">
                <i className="ti-plus mr-2"></i>
                Criar Primeiro Aviso
              </Link>
            </div>
          </RctCollapsibleCard>
        )}
      </div>
    </div>
  )
}

const mapStateToProps = ({ authReducer }) => {
  const { user, account, ownerId } = authReducer
  return { user, account, accountId: account?.ID, ownerId }
}

export default connect(mapStateToProps)(WarningsList)
